# Arch-Scope Project CRUSH Profile

This document outlines the essential commands, conventions, and workflow for the Arch-Scope project.

## Development Workflow

This project uses **Task Master** for task-driven development. For details, refer to the [development workflow guide](mdc:.cursor/rules/dev_workflow.mdc).

- **Listing tasks:** `task-master list`
- **Starting a task:** `task-master next`
- **Expanding a task:** `task-master expand --id=<id> --research`

## Build and Test

This is a **Java Maven** project.

- **Build all modules:** `mvn clean install`
- **Run all tests:** `mvn test`
- **Run a single test:** `mvn -Dtest=TestClassName#methodName test`
- **Run a single test file:** `mvn -Dtest=TestClassName test`
- **Run all tests in a module:** `cd arch-scope-MODULE && mvn test`

## Code Style and Conventions

Code style is managed through **Cursor rules**. For full details, see the [rules directory](mdc:.cursor/rules/).

- **Rule improvement:** Follow the [self-improvement guide](mdc:.cursor/rules/self_improve.mdc) to suggest new rules.

## Committing and Versioning

- **Commit messages:** Follow standard conventions.
- **Versioning:** This project uses a standard versioning scheme.
